/*eslint-disable*/
import React, { useState, useEffect } from 'react';
import moment from 'moment';
import { DataReceivedEvent } from '../../../../utils/constants';
import { ReactComponent as CaptureIcon } from '../../../../assets/icons/Copy.svg';
import { SaasService } from '../../../services/saasServices';
import { useSaasHelpers } from '../../../helpers/helpers';

export function ScreenCaptureButton({
  room,
  screenShareTracks,
  // focusTrack,
  setToastNotification,
  setToastStatus,
  setShowToast,
  setShowPopover,
  className = "lk-button control-bar-button control-bar-button-icon"
}) {
  const [isCapturing, setIsCapturing] = useState(false);
  const [shouldDownload, setShouldDownload] = useState(true); // State to control download
  const { saasHostToken } = useSaasHelpers();



  const uploadScreenshot = async (formData) => {
    try {
      const response = await SaasService.uploadScreenshot(
        formData,
        { "Content-Type": "multipart/form-data" },
        saasHostToken
      );
      if (response.success === 0) {
        console.log("Error uploading screenshot", response);
        throw new Error("Failed to upload screenshot");
      } else if (response.success === 1) {
        console.log("Screenshot uploaded successfully", response);
      }
      return response;
    } catch (error) {
      console.error("Screenshot upload error:", error);
      throw new Error(`Screenshot upload failed: ${error.message}`);
    }
  };

  const captureFromMediaTrack = async (track) => {
    try {
      // Create a temporary video element to capture from the track
      const tempVideo = document.createElement('video');
      tempVideo.srcObject = new MediaStream([track]);
      tempVideo.autoplay = true;
      tempVideo.muted = true;
      tempVideo.playsInline = true;

      // Wait for video to load and play
      await new Promise((resolve, reject) => {
        tempVideo.onloadedmetadata = () => {
          tempVideo.play().then(() => resolve()).catch(reject);
        };
        tempVideo.onerror = reject;
      });

      // Wait a bit more to ensure video is ready
      await new Promise(resolve => {
        setTimeout(() => resolve(), 100);
      });

      if (tempVideo.videoWidth === 0 || tempVideo.videoHeight === 0) {
        throw new Error("Video track has no dimensions");
      }

      // Create canvas to capture the video frame
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Set canvas dimensions to match video's actual dimensions
      canvas.width = tempVideo.videoWidth;
      canvas.height = tempVideo.videoHeight;

      // Draw the current video frame to canvas (this gets the raw content, no CSS filters)
      ctx.drawImage(tempVideo, 0, 0, canvas.width, canvas.height);

      // Clean up temp video
      tempVideo.srcObject = null;

      // Convert canvas to blob and download
      return new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (!blob) {
            reject(new Error("Failed to create blob from video canvas"));
            return;
          }

          const formData = new FormData();
          const filename = `${room?.name}-${moment().format('YYYY-MM-DD-HH-mm-ss')}.png`;
          const file = new File([blob], filename, { type: 'image/png' });
          formData.append('image', file);
          if (room?.roomInfo?.name) {
            formData.append('meeting_uid', room.roomInfo.name);
          }

          console.log("formData", formData);
 
          // Upload to API
          uploadScreenshot(formData);

          // Download locally only if shouldDownload is true
          if (shouldDownload) {
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.download = filename;
            link.href = url;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
          }

          resolve();
        }, 'image/png', 0.95);
      });

    } catch (error) {
      console.error("MediaTrack capture error:", error);
      throw new Error(`MediaTrack capture failed: ${error.message}`);
    }
  };

  const captureVideoElement = async (videoElement) => {
    try {
      // Validate video element
      if (!videoElement || !videoElement.videoWidth || !videoElement.videoHeight) {
        throw new Error("Invalid video element: no dimensions or not loaded");
      }

      console.log("Capturing video element:", videoElement, "Dimensions:", videoElement.videoWidth, "x", videoElement.videoHeight);

      // Create canvas to capture the video frame
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Set canvas dimensions to match video's actual dimensions
      canvas.width = videoElement.videoWidth;
      canvas.height = videoElement.videoHeight;

      // Draw the current video frame to canvas
      ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

      // Convert canvas to blob and download
      return new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (!blob) {
            reject(new Error("Failed to create blob from video canvas"));
            return;
          }

          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.download = `screen-share-capture-${Date.now()}.png`;
          link.href = url;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
          resolve();
        }, 'image/png', 0.95);
      });

    } catch (error) {
      console.error("Video element capture error:", error);
      throw new Error(`Video capture failed: ${error.message}`);
    }
  };

  const broadcastCaptureEvent = async () => {
    if (!room || !room.localParticipant) return;

    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.SCREEN_CAPTURE_TAKEN,
        participantName: room.localParticipant.name || room.localParticipant.identity,
        timestamp: Date.now(),
      })
    );

    await room.localParticipant.publishData(data, {
      reliable: true,
    });
  };

  const handleScreenCapture = async () => {
    if (isCapturing) return;

    // Check if screen sharing is active
    if (!screenShareTracks || screenShareTracks.length === 0) {
      setToastNotification("No active screen share to capture");
      setToastStatus("warning");
      setShowToast(true);
      return;
    }

    setIsCapturing(true);

    try {
      // Strategy 0: Try to capture directly from MediaStreamTrack (works for both blurred and unblurred)
      if (screenShareTracks && screenShareTracks.length > 0) {
        const [screenShareTrack] = screenShareTracks; // Get the first screen share track
        if (screenShareTrack.publication && screenShareTrack.publication.track) {
          const { mediaStreamTrack } = screenShareTrack.publication.track;
          if (mediaStreamTrack && mediaStreamTrack.readyState === 'live') {
            try {
              await captureFromMediaTrack(mediaStreamTrack);

              // Broadcast to all participants
              await broadcastCaptureEvent();

              // Show success toast
              setToastNotification("Screenshot captured successfully");
              setToastStatus("success");
              setShowToast(true);

              // Close the popover
              if (setShowPopover) {
                setShowPopover(false);
              }
              return; // Success, exit early
            } catch (trackError) {
              console.warn("MediaStreamTrack capture failed, falling back to video element capture:", trackError);
            }
          }
        }
      }

      // Fallback: Find the main screen share video element (the big one in focus layout)
      let screenShareVideo = null;

      // Strategy 1: Look for video in the focus layout container (the main big screen share)
      // Exclude blurred videos (local participant's view) by checking style attribute
      const focusLayoutContainer = document.querySelector('.lk-focus-layout-container');
      if (focusLayoutContainer) {
        const focusVideos = focusLayoutContainer.querySelectorAll('video');
        for (const focusVideo of focusVideos) {
          // Skip blurred videos (local participant's screen share view)
          const style = focusVideo.getAttribute('style') || '';
          if (!style.includes('blur') && focusVideo && focusVideo.videoWidth > 0 && focusVideo.videoHeight > 0) {
            // Verify this is actually a screen share video by checking its tracks
            if (focusVideo.srcObject && focusVideo.srcObject.getVideoTracks) {
              const tracks = focusVideo.srcObject.getVideoTracks();
              for (const track of tracks) {
                // Check if this track matches any of our screen share tracks
                const matchingScreenShareTrack = screenShareTracks.find(st =>
                  st.publication && st.publication.track &&
                  track === st.publication.track.mediaStreamTrack
                );
                if (matchingScreenShareTrack) {
                  screenShareVideo = focusVideo;
                  console.log("Found main screen share video in focus layout:", focusVideo);
                  break;
                }
              }
              if (screenShareVideo) break;
            }
          }
        }
      }

      // Strategy 2: Look for the largest video element with screen share content (excluding blurred)
      if (!screenShareVideo) {
        const videoElements = document.querySelectorAll('video');
        let largestVideo = null;
        let largestArea = 0;

        for (const video of videoElements) {
          // Skip blurred videos (local participant's screen share view)
          const style = video.getAttribute('style') || '';
          if (!style.includes('blur') && video.srcObject && video.srcObject.getVideoTracks &&
              video.videoWidth > 0 && video.videoHeight > 0) {
            const tracks = video.srcObject.getVideoTracks();

            // Check if any track in this video matches our screen share tracks
            for (const track of tracks) {
              const matchingScreenShareTrack = screenShareTracks.find(st =>
                st.publication && st.publication.track &&
                track === st.publication.track.mediaStreamTrack
              );
              if (matchingScreenShareTrack) {
                const area = video.clientWidth * video.clientHeight;
                if (area > largestArea) {
                  largestArea = area;
                  largestVideo = video;
                }
                break;
              }
            }
          }
        }

        if (largestVideo) {
          screenShareVideo = largestVideo;
          console.log("Found largest screen share video element:", largestVideo, "Area:", largestArea);
        }
      }

      // Strategy 3: Fallback - look for any screen share video by track characteristics (excluding blurred)
      if (!screenShareVideo) {
        const videoElements = document.querySelectorAll('video');
        for (const video of videoElements) {
          // Skip blurred videos (local participant's screen share view)
          const style = video.getAttribute('style') || '';
          if (!style.includes('blur') && video.srcObject && video.srcObject.getVideoTracks &&
              video.videoWidth > 0 && video.videoHeight > 0) {
            const tracks = video.srcObject.getVideoTracks();
            for (const track of tracks) {
              // Check if this looks like a screen share track
              if (track.label && (
                track.label.includes('screen') ||
                track.label.includes('Screen') ||
                track.label.includes('display') ||
                track.label.includes('Display') ||
                track.label.includes('window') ||
                track.label.includes('Window')
              )) {
                screenShareVideo = video;
                console.log("Found screen share video via track label:", track.label, video);
                break;
              }
            }
            if (screenShareVideo) break;
          }
        }
      }

      if (!screenShareVideo) {
        console.error("No screen share video found. Available screen share tracks:", screenShareTracks.length);
        throw new Error("Screen share video element not found. The video may not be loaded yet.");
      }

      console.log("Capturing screen share video:", screenShareVideo, "Dimensions:", screenShareVideo.videoWidth, "x", screenShareVideo.videoHeight);

      // Capture only the video content (the actual shared screen)
      await captureVideoElement(screenShareVideo);

      // Broadcast to all participants
      await broadcastCaptureEvent();

      // Show success toast
      setToastNotification("Screenshot captured successfully");
      setToastStatus("success");
      setShowToast(true);

      // Close the popover
      if (setShowPopover) {
        setShowPopover(false);
      }

    } catch (error) {
      console.error("Screen capture failed:", error);
      setToastNotification(`Screen capture failed: ${error.message}`);
      setToastStatus("error");
      setShowToast(true);
    } finally {
      setIsCapturing(false);
    }
  };

  useEffect(() => {

    const handleKeyDown = (event) => {
      if (event.shiftKey && event.key.toLowerCase() === 's') {
        if (screenShareTracks && screenShareTracks.length > 0 && !isCapturing) {
          event.preventDefault(); 
          handleScreenCapture();
        }
      }
    };

    const handleKeyUp = () => {
      // No need for key tracking with simple Shift + S
    };

    // Add event listeners
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    // Cleanup event listeners on unmount
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [screenShareTracks, isCapturing]); // Dependencies to ensure latest state

  // Only render if screen sharing is active
  if (!screenShareTracks || screenShareTracks.length === 0) {
    return null;
  }

  return (
    <div
      onClick={handleScreenCapture}
      className="lk-button control-bar-button control-bar-button-icon"
    >
      <CaptureIcon
        style={{ color: isCapturing ? '#1890ff' : '#fff' }}
      />
    </div>
  );
}
