@import "../../../../styles/variables";

// Screen Capture Button Styles
.screen-capture-button {

  // Settings menu version (when used in settings)
  &.settings-menu-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: transparent;
    border-radius: 8px;
    width: 100%;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    .screen-capture-icon {
      margin-right: 12px;

      svg {
        width: 24px;
        height: 24px;
      }
    }

    .screen-capture-text {
      flex: 1;
      color: white;
      font-size: 14px;
    }

    .screen-capture-toggle {
      margin-left: auto;
      cursor: pointer;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      transition: background-color 0.2s ease;
    }
  }
}

// Loading animation for capturing state
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.screen-capture-loading {
  animation: spin 1s linear infinite;
}
