@import "../../../../styles/variables";

// Screen Capture Button Styles
.screen-capture-button {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
  
  // Settings menu version (default)
  &.settings-menu-item {
    padding: 12px 16px;
    background-color: transparent;
    border-radius: 8px;
    width: 100%;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
    
    .screen-capture-icon {
      margin-right: 12px;
      
      svg {
        width: 24px;
        height: 24px;
      }
    }
    
    .screen-capture-text {
      flex: 1;
      color: white;
      font-size: 14px;
    }
    
    .screen-capture-toggle {
      margin-left: auto;
      cursor: pointer;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      transition: background-color 0.2s ease;
    }
  }
  
  // Control bar version
  &.control-bar-version {
    padding: 8px 12px;
    background-color: transparent;
    border-radius: 8px;
    min-width: 40px;
    min-height: 40px;
    
    &:hover {
      background-color: #2d2d38;
    }
    
    .screen-capture-icon {
      svg {
        width: 20px;
        height: 20px;
      }
    }
    
    // Hide text and toggle in control bar
    .screen-capture-text,
    .screen-capture-toggle {
      display: none;
    }
  }
  
  // Icon states
  .screen-capture-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    
    svg {
      transition: color 0.2s ease;
    }
  }
  
  // Capturing state
  &.capturing {
    .screen-capture-icon svg {
      color: #1890ff;
    }
  }
}

// Control bar wrapper
.control-bar-screen-capture-wrapper {
  display: inline-flex;
  align-items: center;
}
