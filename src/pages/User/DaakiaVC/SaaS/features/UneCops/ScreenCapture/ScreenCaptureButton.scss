// Screen Capture Button specific styling
.screen-capture-control-button {
  // Make it slightly bigger than other control bar buttons
  padding: 10px 14px !important;
  min-width: 44px !important;
  min-height: 44px !important;

  // Custom styling for screen capture button
  border-radius: 10px !important;

  svg {
    width: 22px !important;
    height: 22px !important;
  }

  // Enhanced hover effect
  &:hover {
    background-color: #2d2d38 !important;
    transform: scale(1.05);
  }

  // Capturing state styling
  &.capturing {
    background-color: rgba(24, 144, 255, 0.1) !important;
    border: 1px solid rgba(24, 144, 255, 0.3);
  }
}

// Loading animation for capturing state
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.screen-capture-loading {
  animation: spin 1s linear infinite;
}
